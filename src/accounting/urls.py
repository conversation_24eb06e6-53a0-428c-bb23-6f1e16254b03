from django.contrib import admin
from django.urls import (
    include,
    path,
)
from rest_framework.routers import <PERSON>fa<PERSON><PERSON><PERSON><PERSON>

from accounting.views import (
    MoneyTransfersView,
    OrderActionViewSet,
    OrderOverviewView,
    PaypalExportView,
)

router = DefaultRouter()
router.register('orders', OrderActionViewSet, basename='order-actions')

urlpatterns = [
    path('api/', include(router.urls)),
    path(
        'order_info/<int:pk>/',
        admin.site.admin_view(OrderOverviewView.as_view()),
        name='order-overview',
    ),
    path(
        'paypal_import/',
        admin.site.admin_view(PaypalExportView.as_view()),
        name='paypal-import',
    ),
    path(
        'money_transfers/',
        admin.site.admin_view(MoneyTransfersView.as_view()),
        name='money-transfers',
    ),
]
