{% extends "admin/base_site.html" %}
{% load static %}

{% block extrastyle %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="{% static 'css/order_overview.css' %}">
<script src="https://cdn.jsdelivr.net/npm/vue@3.3.4/dist/vue.global.js"></script>
{% endblock %}

{% block content %}
<div id="orderOverviewApp" class="order-overview-container">
    {% include "accounting/partials/_order_header.html" %}

    <div class="sections-container">
        {% include "accounting/partials/_billing_section.html" %}
        {% include "accounting/partials/_delivery_section.html" %}
        {% include "accounting/partials/_history_section.html" %}
    </div>
</div>

<script>
    const { createApp } = Vue;

    createApp({
        data() {
            return {
                orderId: {{ order.id }},
                actionsMenuOpen: false,
                sections: {
                    billing: true,
                    invoices: true,
                    delivery: true,
                    history: true
                }
            }
        },
        methods: {
        toggleActionsMenu() {
            this.actionsMenuOpen = !this.actionsMenuOpen;
        },
        toggleSection(sectionName) {
            this.sections[sectionName] = !this.sections[sectionName];
        },
        handleAction(action) {
            this.actionsMenuOpen = false;

            // TODO: Implement actual action handlers
            const actions = {
                validate: () => this.validateOrder(),
                recalculate: () => this.recalculateOrder(),
                edit: () => this.editOrder(),
                push: () => this.pushToProduction()
            };

            if (actions[action]) {
                actions[action]();
            } else {
                alert(`Action "${action}" will be implemented.`);
            }
        },
        async validateOrder() {
            try {
                const response = await fetch(`/admin/accounting/api/orders/${this.orderId}/validate/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': this.getCookie('csrftoken'),
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    alert('Order validated successfully!');
                    location.reload();
                } else {
                    const errors = await response.json();
                    console.log(errors);
                    console.log('DUPSKO');
                    alert('Validation failed. ' + errors.errors);
                }
            } catch (error) {
                console.error('Validation error:', error);
                alert('An error occurred during validation.');
            }
        },
        async recalculateOrder() {
            try {
                const response = await fetch(`/admin/accounting/api/orders/${this.orderId}/recalculate/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': this.getCookie('csrftoken'),
                        'Content-Type': 'application/json',
                    }
                });

                if (response.ok) {
                    alert('Order recalculated successfully!');
                    location.reload();
                } else {
                    alert('Recalculation failed. Please try again.');
                }
            } catch (error) {
                console.error('Validation error:', error);
                alert('An error occurred during recalculation.');
            }
        },
        editOrder() {
            window.location.href = `/admin/orders/order/${this.orderId}/`;
        },
        async pushToProduction() {
            if (!confirm('Are you sure you want to push this order to production?')) {
                return;
            }
            alert('Push to production functionality will be implemented');
            // TODO: Implement push to production API call
        },
        getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    },
        mounted() {
        // Close dropdown when clicking outside
        document.addEventListener('click', (event) => {
            const dropdown = this.$el.querySelector('.actions-dropdown');
            if (dropdown && !dropdown.contains(event.target)) {
                this.actionsMenuOpen = false;
            }
        });
    }
}).mount('#orderOverviewApp');
</script>
{% endblock %}
