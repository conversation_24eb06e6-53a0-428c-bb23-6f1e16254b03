<!-- Top Bar -->
<div class="order-header">
    <div class="order-header-info">
        <div class="order-id">Order #{{ order.id }}</div>
        <span class="order-badge badge-success">{{ order.get_status_display }}</span>
        <span class="order-badge badge-info">{{ order.get_order_type_display }}</span>
    </div>

    <div class="actions-dropdown">
        <button class="actions-btn" @click="toggleActionsMenu">
            <i class="fa fa-bars"></i>
            Actions
        </button>
        <div class="actions-menu" :class="{ active: actionsMenuOpen }">
            <button class="action-item" @click="handleAction('validate')">
                <i class="fa fa-check-circle"></i>
                Validate Order
            </button>
            <button class="action-item" @click="handleAction('recalculate')">
                <i class="fa fa-refresh"></i>
                Recalculate
            </button>
            <button class="action-item" @click="handleAction('edit')">
                <i class="fa fa-pencil"></i>
                Edit Manually
            </button>
            <button class="action-item" @click="handleAction('push')">
                <i class="fa fa-cloud-upload"></i>
                Push to Production
            </button>
        </div>
    </div>
</div>

<!-- Billing Information Section -->
<div class="section" :class="{ collapsed: !sections.billing }" style="margin-bottom: 24px;">
    <div class="section-header" @click="toggleSection('billing')">
        <div class="section-title">
            <i class="fa fa-credit-card section-icon"></i>
            Billing Information
        </div>
        <i class="fa fa-chevron-down toggle-icon"></i>
    </div>
    <div class="section-content">
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Customer Name</div>
                <div class="info-value">
                    <a href="/admin/user_profile/userprofile/{{ order.owner.profile.id }}/">
                        {{ order.get_customer_as_string }}
                    </a>
                </div>
            </div>
            <div class="info-item">
                <div class="info-label">Email</div>
                <div class="info-value">{{ order.email }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Country</div>
                <div class="info-value">{{ order.get_country }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Created At</div>
                <div class="info-value">{{ order.created_at|date:"Y-m-d H:i" }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Paid At</div>
                <div class="info-value">
                    {% if order.paid_at %}
                    {{ order.paid_at|date:"Y-m-d H:i" }}
                    {% else %}
                    <span style="color: var(--text-secondary);">Not paid</span>
                    {% endif %}
                </div>
            </div>
            <div class="info-item">
                <div class="info-label">VAT Type</div>
                <div class="info-value">{{ order.get_vat_type_display }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Customer Type</div>
                <div class="info-value">
                    {% if order.vat %}
                    <span class="order-badge badge-info">B2B</span>
                    {% else %}
                    <span class="order-badge badge-success">B2C</span>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
