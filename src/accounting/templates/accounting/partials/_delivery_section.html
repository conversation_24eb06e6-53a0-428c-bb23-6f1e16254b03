<!-- Delivery Section -->
<div class="section" :class="{ collapsed: !sections.delivery }">
    <div class="section-header" @click="toggleSection('delivery')">
        <div class="section-title">
            <i class="fa fa-truck section-icon"></i>
            Delivery & Production
        </div>
        <i class="fa fa-chevron-down toggle-icon"></i>
    </div>
    <div class="section-content">
        <div class="info-grid">
            <div class="info-item">
                <div class="info-label">Order Status</div>
                <div class="info-value">
                    <span class="order-badge badge-success">{{ order.get_status_display }}</span>
                </div>
            </div>
            <div class="info-item">
                <div class="info-label">Created At</div>
                <div class="info-value">{{ order.created_at }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Paid At</div>
                <div class="info-value">{{ order.paid_at|default:"Not paid" }}</div>
            </div>
            <div class="info-item">
                <div class="info-label">Items Count</div>
                <div class="info-value">{{ order.items.count }} item{{ order.items.count|pluralize }}</div>
            </div>
        </div>

        <h4 class="section-subtitle">Products in Production</h4>
        <table class="data-table">
            <thead>
                <tr>
                    <th>Product ID</th>
                    <th>Manufacturer</th>
                    <th>Status</th>
                    <th>Priority</th>
                    <th>Created</th>
                </tr>
            </thead>
            <tbody>
                {% for product in order.product_set.all %}
                <tr>
                    <td><a href="/admin/producers/product/{{ product.id }}/">{{ product.id }}</a></td>
                    <td>{{ product.manufactor }}</td>
                    <td>{{ product.get_status_display }}</td>
                    <td>{{ product.get_priority_display|default:"NORMAL" }}</td>
                    <td>{{ product.created_at }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="empty-state">
                        <i class="fa fa-cube"></i>
                        <p>No products in production</p>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

