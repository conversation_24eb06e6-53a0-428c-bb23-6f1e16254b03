<!-- History Section -->
<div class="section" :class="{ collapsed: !sections.history }">
    <div class="section-header" @click="toggleSection('history')">
        <div class="section-title">
            <i class="fa fa-history section-icon"></i>
            History & Activity
        </div>
        <i class="fa fa-chevron-down toggle-icon"></i>
    </div>
    <div class="section-content">
        <h4 class="section-subtitle">Payment History</h4>
        <table class="data-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Payment Method</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                {% for transaction in order.transaction_description %}
                <tr>
                    <td>{{ transaction.date }}</td>
                    <td>{{ transaction.payment_method }}</td>
                    <td>
                        {% if transaction.success %}
                        <span class="order-badge badge-success">Paid</span>
                        {% else %}
                        <span class="order-badge badge-warning">{{ transaction.refusal_reason }}</span>
                        {% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="3" class="empty-state">No payment history</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <h4 class="section-subtitle">Order Activity Log</h4>
        <div class="timeline">
            {% for log in order.match_log %}
            <div class="timeline-item">
                <div class="timeline-dot"></div>
                <div class="timeline-content">
                    <div class="timeline-date">{{ log.created_at|default:"" }}</div>
                    <p class="timeline-text">
                        <strong>{{ log.action }}</strong> by {{ log.user }}
                        <a href="/admin/logger/log/{{ log.id }}/" style="margin-left: 8px; font-size: 12px;">View details</a>
                    </p>
                </div>
            </div>
            {% empty %}
            <div class="empty-state">
                <i class="fa fa-clipboard"></i>
                <p>No activity logs found</p>
            </div>
            {% endfor %}
        </div>

        <h4 class="section-subtitle">Quick Links</h4>
        <div class="info-grid">
            <div class="info-item">
                <div class="info-value">
                    <a href="{% url "admin:orders_order_changelist" %}?id={{ order.id }}">→ View in Orders List</a>
                </div>
            </div>
            <div class="info-item">
                <div class="info-value">
                    <a href="{% url "admin:invoice_invoice_changelist" %}?order_id={{ order.id }}">→ View Invoices</a>
                </div>
            </div>
            <div class="info-item">
                <div class="info-value">
                    <a href="{% url "admin:producers_product_changelist" %}?order_id={{ order.id }}">→ View Products</a>
                </div>
            </div>
            <div class="info-item">
                <div class="info-value">
                    <a href="/admin/orders/order/{{ order.id }}/">→ Edit Order</a>
                </div>
            </div>
        </div>
    </div>
</div>

