<!-- Invoices & Payments Section -->
<div class="section" :class="{ collapsed: !sections.invoices }">
    <div class="section-header" @click="toggleSection('invoices')">
        <div class="section-title">
            <i class="fa fa-file-text-o section-icon"></i>
            Invoices & Payments
        </div>
        <i class="fa fa-chevron-down toggle-icon"></i>
    </div>
    <div class="section-content">
        <h4 class="section-subtitle">Invoices</h4>
        {% for invoice in order.invoice_set.all %}
        <div style="margin-bottom: 24px;">
            <div
                style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; padding: 12px; background: var(--bg-light); border-radius: 8px;">
                <div>
                    <strong>Invoice:</strong>
                    <a href="/admin/invoice/invoice/{{ invoice.id }}/">
                        {% if invoice.pretty_id%}
                        {{ invoice.pretty_id }}
                        {% else %}#{{ invoice.id }}{% endif %}
                    </a>
                </div>
                <div>
                    {% if invoice.pdf %}
                    <a href="{{ invoice.pdf.url }}" class="action-item"
                        style="display: inline-flex; align-items: center; gap: 8px; padding: 8px 16px; background: var(--primary-color); color: white; text-decoration: none; border-radius: 6px;">
                        <i class="fa fa-download"></i>
                        Download PDF
                    </a>
                    {% else %}
                    <span style="color: var(--text-secondary);">No PDF available</span>
                    {% endif %}
                </div>
            </div>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Item Name</th>
                        <th>Currency</th>
                        <th>Qty</th>
                        <th>Net Price</th>
                        <th>Discount</th>
                        <th>Net Value</th>
                        <th>VAT Rate</th>
                        <th>VAT Amount</th>
                        <th>Gross Price</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice.invoice_items.all %}
                    <tr>
                        <td>{{ item.item_name }}</td>
                        <td>{{ invoice.currency_symbol }}</td>
                        <td>{{ item.quantity }}</td>
                        <td>{{ item.net_price }}</td>
                        <td>{{ item.discount_value }}</td>
                        <td>{{ item.net_value }}</td>
                        <td>{% widthratio item.vat_rate 1 100 %}%</td>
                        <td>{{ item.vat_amount }}</td>
                        <td>{{ item.gross_price }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="empty-state">
                            <i class="fa fa-file-text-o"></i>
                            <p>No items in this invoice</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% empty %}
        <div class="empty-state" style="padding: 24px; text-align: center;">
            <i class="fa fa-file-text-o"></i>
            <p>No invoices found</p>
        </div>
        {% endfor %}

        <h4 class="section-subtitle">Money Transfers</h4>
        <table class="data-table">
            <thead>
                <tr>
                    <th>Source</th>
                    <th>Type</th>
                    <th>Amount</th>
                    <th>Date</th>
                </tr>
            </thead>
            <tbody>
                {% for mt in order.moneytransfer_set.all %}
                <tr>
                    <td><a href="/admin/accounting/moneytransfer/{{ mt.id }}/">{{ mt.get_payment_source_display }}</a>
                    </td>
                    <td>{{ mt.get_payment_type_display }}</td>
                    <td>{{ mt.amount }} {{ mt.get_currency_display }}</td>
                    <td>{{ mt.issue_date }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="4" class="empty-state">
                        <i class="fa fa-dollar"></i>
                        <p>No money transfers found</p>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>