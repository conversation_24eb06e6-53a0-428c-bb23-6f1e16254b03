:root {
    --primary-color: #2563eb;
    --primary-hover: #1d4ed8;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --bg-light: #f9fafb;
    --bg-white: #ffffff;
    --text-primary: #111827;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

* {
    box-sizing: border-box;
}

body {
    background-color: var(--bg-light);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    color: var(--text-primary);
    margin: 0;
    padding: 0;
}

.order-overview-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
}

/* Top Bar */
.order-header {
    background: var(--bg-white);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.order-header-info {
    display: flex;
    align-items: center;
    gap: 24px;
    flex: 1;
    flex-wrap: wrap;
}

.order-id {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
}

.order-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-success {
    background-color: #d1fae5;
    color: #065f46;
}

.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-info {
    background-color: #dbeafe;
    color: #1e40af;
}

/* Actions Menu */
.actions-dropdown {
    position: relative;
}

.actions-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
}

.actions-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.actions-menu {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background: var(--bg-white);
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    display: none;
    z-index: 1000;
    overflow: hidden;
}

.actions-menu.active {
    display: block;
}

.action-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    color: var(--text-primary);
    text-decoration: none;
    transition: background 0.2s;
    cursor: pointer;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 14px;
}

.action-item:hover {
    background-color: var(--bg-light);
}

.action-item i {
    width: 18px;
    text-align: center;
    flex-shrink: 0;
}

/* Sections */
.sections-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.section {
    background: var(--bg-white);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.3s;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    cursor: pointer;
    user-select: none;
    transition: background 0.2s;
}

.section-header:hover {
    background-color: var(--bg-light);
}

.section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 18px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.section-icon {
    width: 24px;
    font-size: 20px;
    text-align: center;
    color: var(--primary-color);
}

.toggle-icon {
    width: 20px;
    font-size: 20px;
    text-align: center;
    color: var(--text-secondary);
    transition: transform 0.3s;
}

.section.collapsed .toggle-icon {
    transform: rotate(-180deg);
}

.section-content {
    padding: 0 24px 24px 24px;
    display: block;
}

.section.collapsed .section-content {
    display: none;
}

/* Content Grid */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.info-label {
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--text-secondary);
}

.info-value {
    font-size: 14px;
    color: var(--text-primary);
    line-height: 1.5;
}

.info-value a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.info-value a:hover {
    text-decoration: underline;
}

/* Tables */
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 16px;
}

.data-table th {
    text-align: left;
    padding: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--text-secondary);
    border-bottom: 2px solid var(--border-color);
}

.data-table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
    font-size: 14px;
}

.data-table tbody tr:hover {
    background-color: transparent;
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

/* History Timeline */
.timeline {
    position: relative;
    padding-left: 32px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-color);
}

.timeline-item {
    position: relative;
    padding-bottom: 24px;
}

.timeline-item:last-child {
    padding-bottom: 0;
}

.timeline-dot {
    position: absolute;
    left: -28px;
    top: 4px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    border: 3px solid var(--bg-white);
    box-shadow: 0 0 0 2px var(--primary-color);
}

.timeline-content {
    background: var(--bg-light);
    padding: 12px 16px;
    border-radius: 8px;
}

.timeline-date {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.timeline-text {
    font-size: 14px;
    color: var(--text-primary);
    margin: 0;
}

/* Section Headers */
.section-subtitle {
    margin-top: 24px;
    margin-bottom: 12px;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    color: var(--text-secondary);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 48px;
    display: block;
    margin-bottom: 12px;
    opacity: 0.5;
}

/* Responsive */
@media (max-width: 768px) {
    .order-overview-container {
        padding: 16px;
    }

    .order-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .data-table {
        font-size: 12px;
    }

    .data-table th,
    .data-table td {
        padding: 8px;
    }
}