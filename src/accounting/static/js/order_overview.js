// Define functions in global scope
function toggleActionsMenu() {
    const menu = document.getElementById('actionsMenu');
    menu.classList.toggle('active');
}

function toggleSection(sectionId) {
    const section = document.getElementById(sectionId);
    section.classList.toggle('collapsed');
}

function handleAction(action) {
    // Close the menu
    document.getElementById('actionsMenu').classList.remove('active');

    // Placeholder for action handlers
    alert(`Action "${action}" will be implemented. This will handle: validate, recalculate, edit manually, and push to prod.`);

    // TODO: Implement actual action handlers
    // Example:
    // if (action === 'validate') {
    //     fetch(`/admin/accounting/order_info/${orderId}/validate/`, {
    //         method: 'POST',
    //         headers: {
    //             'X-CSRFToken': getCookie('csrftoken'),
    //         }
    //     })
    //     .then(response => response.json())
    //     .then(data => { /* handle response */ });
    // }
}

// Helper function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Initialize event listeners when DOM is ready
(function() {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initOrderOverview);
    } else {
        initOrderOverview();
    }

    function initOrderOverview() {
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.querySelector('.actions-dropdown');
            if (dropdown && !dropdown.contains(event.target)) {
                const menu = document.getElementById('actionsMenu');
                if (menu) {
                    menu.classList.remove('active');
                }
            }
        });
    }
})();

