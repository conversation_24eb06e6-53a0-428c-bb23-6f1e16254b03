from unittest import mock
from unittest.mock import MagicMock

from django.contrib.auth.models import AnonymousUser
from django.test import RequestFactory

import pytest

from regions.middleware import (
    ProfileRegionCacheMiddleware,
    RegionMiddleware,
)


@pytest.mark.django_db
class TestProfileRegionCacheMiddleware:
    middleware_class = ProfileRegionCacheMiddleware
    cached_properties = {
        'cached_profile',
        'cached_region',
        'cached_currency',
        'cached_country',
        'cached_abtests',
    }

    @pytest.mark.parametrize(
        'page',
        [
            '/cs/',
            '/cs/user_overview/sample_number/',
            '/admin/',
            '/admin/pages/producer_panel/',
        ],
    )
    def test_if_middleware_is_not_caching_admin_and_cs_pages_regions(
        self,
        rf,
        admin_user,
        page,
    ):
        get_response = MagicMock()
        middleware = self.middleware_class(get_response)
        request = rf.get(page)
        request.user = admin_user
        middleware.process_request(request)
        for cached_property in self.cached_properties:
            assert not hasattr(request, cached_property)

    def test_if_middleware_creates_cached_properties(self, rf, admin_user):
        get_response = MagicMock()
        middleware = self.middleware_class(get_response)
        request = rf.get('/other-page/')
        request.user = admin_user
        middleware.process_request(request)
        for cached_property in self.cached_properties:
            assert hasattr(request, cached_property)


@pytest.mark.django_db
class TestRegionMiddleware:
    @pytest.fixture(autouse=True)
    def setup(self, user_factory, country_factory):
        self.uk = country_factory(united_kingdom=True, code='UK')
        self.user = user_factory(profile__region=country_factory(germany=True).region)
        self.request_factory = RequestFactory()
        self.middleware = RegionMiddleware(get_response=MagicMock())

    @mock.patch('regions.middleware.change_region')
    def test_process_request_with_authenticated_user_and_valid_url_change_region(
        self, mock_change_region
    ):
        request = self.request_factory.get(
            '/api/some/path', HTTP_REQUESTURL='http://example.com/en-uk/'
        )
        request.user = self.user
        self.middleware.process_request(request)
        mock_change_region.assert_called_once()
        mock_change_region.assert_called_once_with(
            self.user.profile,
            self.uk.region,
        )

    @mock.patch('user_profile.models.models.UserProfile.change_region')
    def test_process_request_with_authenticated_user_and_valid_url_dont_change_region(
        self, mock_change_region
    ):
        request = self.request_factory.get(
            '/api/some/path', HTTP_REQUESTURL='http://example.com/de-de/'
        )
        request.user = self.user
        self.middleware.process_request(request)
        mock_change_region.assert_not_called()

    @mock.patch('user_profile.models.models.UserProfile.change_region')
    def test_process_request_with_authenticated_user_and_invalid_url(
        self, mock_change_region
    ):
        request = self.request_factory.get(
            '/api/some/path', HTTP_REQUESTURL='http://example.com/INVALID/'
        )
        request.user = self.user
        self.middleware.process_request(request)
        mock_change_region.assert_not_called()

    @mock.patch('user_profile.models.models.UserProfile.change_region')
    def test_process_request_with_unauthenticated_user(self, mock_change_region):
        request = self.request_factory.get(
            '/api/some/path', HTTP_REQUESTURL='http://example.com/de-de/'
        )
        request.user = AnonymousUser()
        self.middleware.process_request(request)
        assert not hasattr(request.user, 'profile')
        mock_change_region.assert_not_called()

    @mock.patch('user_profile.models.models.UserProfile.change_region')
    def test_process_request_with_excluded_path(self, mock_change_region):
        request = self.request_factory.get(
            '/api/v1/regions/change_region/',
            HTTP_REQUESTURL='http://example.com/en-uk/',
        )
        request.user = self.user
        self.middleware.process_request(request)
        mock_change_region.assert_not_called()
