from typing import TYPE_CHECKING

from carts.services.cart_service import CartService
from payments.models import Transaction
from regions.models import Region

if TYPE_CHECKING:
    from user_profile.models import UserProfile


def change_region(profile: 'UserProfile', region: Region) -> None:
    """Change region of the profile and related cart and order. Update fields also at
    transaction, if exists.

    Recalculates cart after region change.
    """
    profile.change_region(region)
    cart = CartService.get_cart(profile.user)
    if not cart:
        return

    cart_service = CartService(cart)
    cart_service.change_region(region)

    order = cart.order
    if not order:
        return

    cart_service.sync_with_order()
    Transaction.sync_with_order(order)
