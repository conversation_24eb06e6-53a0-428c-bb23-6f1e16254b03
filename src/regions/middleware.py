from django.utils.deprecation import MiddlewareMixin

from abtests.models import ABTest
from regions.change_region import change_region
from regions.models import Region
from regions.utils import (
    get_region_code_from_url,
    get_region_from_url,
)


class ProfileRegionCacheMiddleware(MiddlewareMixin):
    EXCLUDED_PATHS = {'/admin/', '/cs/'}

    def process_request(self, request):
        if any(
            request.META['PATH_INFO'].startswith(path) for path in self.EXCLUDED_PATHS
        ):
            return
        if request.path.startswith('/api/v1/regions/change_region/'):
            return
        if (
            getattr(request, 'cached_profile', None) is None
            and request.user.is_authenticated
            and request.user.profile
        ):
            request.cached_profile = request.user.profile
        if getattr(request, 'cached_profile', None) is not None:
            region = request.cached_profile.region
            if region is None:
                region = Region.get_other()
            request.cached_region = region
            request.cached_currency = region.get_currency()
            request.cached_country = region.get_country()
            request.cached_abtests = [
                ab_test
                for ab_test in ABTest.objects.get_tests_cached()
                if ab_test.active and not ab_test.feature_flag
            ]


class RegionMiddleware(MiddlewareMixin):
    """This is used to force user region based on the request url"""

    def process_request(self, request):
        if (
            '/api/' not in request.path
            or request.path.startswith('/api/v1/regions/change_region/')
            or not request.user.is_authenticated
        ):
            return

        profile_region = request.user.profile.get_region()
        request_url = request.META.get('HTTP_REQUESTURL')
        if not request_url:
            return
        try:
            request_url_region_code = get_region_code_from_url(request_url)
        except (IndexError, AttributeError):
            return

        if request_url_region_code.lower() != profile_region.country.code.lower():
            new_region = get_region_from_url(request_url)
            if not new_region:
                return

            change_region(request.user.profile, new_region)
