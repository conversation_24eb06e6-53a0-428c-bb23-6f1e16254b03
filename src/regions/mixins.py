from __future__ import annotations

import logging

from decimal import Decimal
from typing import TYPE_CHECKING

from django.utils import timezone
from django.utils.functional import cached_property

from custom.utils.decorators import (
    cache_model_method,
    clear_cache_for_kwargs,
)
from regions.cached_region import CachedRegionData
from regions.constants import (
    REGION_CACHE_PERIOD,
    REGION_RATE_CACHE_PERIOD,
)

if TYPE_CHECKING:
    from regions.services.regionalized_price import RegionCalculationsObject
    from regions.types import RegionLikeObject

logger = logging.getLogger('cstm')


class RegionalizedMixin:
    @property
    def _region(self):
        if hasattr(self, 'region'):
            return self.region
        raise NotImplementedError

    def change_region(self, new_region):
        if hasattr(self, 'region'):
            self.region = new_region
            return self
        raise NotImplementedError

    @cache_model_method(cache_period=REGION_CACHE_PERIOD)
    def get_region(self, dont_use_cache=False):
        from regions.models import Region

        if self._region:
            return self._region
        return Region.get_other()

    def display_regionalized(self, value: Decimal | int | float) -> str:
        cached_region_data = self.get_region().cached_region_data
        return cached_region_data.get_format_price(value)

    def clear_methods_cache(self):
        clear_cache_for_kwargs(self, self.get_region)


class RateCopyOnSaveMixin(object):
    def save(self, *args, **kwargs):
        self_class = self.__class__
        try:
            if self.id:
                o = self_class.objects.get(pk=self.id)
                o_copy_dict = dict(  # noqa: C404
                    [
                        (f.name, getattr(o, f.name))
                        for f in self_class._meta.fields
                        if f.name not in ['id']
                    ]
                )
                o_copy = self_class(**o_copy_dict)
                o_copy.save_base()
            self.time = timezone.now()
        except self_class.DoesNotExist:
            logger.warning(
                'Trying to manually change the id? Alright. '
                'Take this sword and go! o--[=====/'
            )
        super(RateCopyOnSaveMixin, self).save(*args, **kwargs)


class RatedInTimeMixin(object):
    @cached_property
    @cache_model_method(cache_period=REGION_RATE_CACHE_PERIOD)
    def current_rate(self):
        assert hasattr(self, 'rates')
        rate = self.rates.first()
        if rate is None:
            rate = self.rates.model.objects.create(
                **{
                    'rate': 1,
                    list(self.rates.core_filters.keys())[0]: self,  # noqa: RUF015
                }
            )
        return rate


class RegionMixin:
    @cached_property
    def region(self) -> CachedRegionData | None:
        from regions.models import Region

        if self.region_name:
            try:
                return Region.objects.get(name=self.region_name).cached_region_data
            except Region.DoesNotExist:
                pass
        if self.request.user.is_authenticated:
            return self.request.user.profile.cached_region_data

    @property
    def region_name(self) -> str:
        return self.request.query_params.get('regionName', '')


class RegionCurrencySerializerMixin:
    context: dict

    @property
    def region(self) -> RegionLikeObject | None:
        return self.context.get('region', None)

    @property
    def region_calculations_object(self) -> RegionCalculationsObject | None:
        return self.context.get('region_calculations_object', None)

    @property
    def currency_rate(self) -> Decimal:
        from gallery.services.prices_for_serializers import get_currency_rate

        currency_rate = self.context.get('currency_rate', None)
        if not currency_rate:
            return get_currency_rate(self.region)
        return currency_rate
