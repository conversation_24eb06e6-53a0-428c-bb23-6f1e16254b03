import calendar
import csv
import hashlib
import inspect
import io
import json
import math
import os.path
import re
import sys
import time

from collections import (
    OrderedDict,
    defaultdict,
)
from datetime import (
    datetime,
    timedelta,
)
from decimal import Decimal
from itertools import groupby
from urllib.parse import unquote

from django.conf import settings
from django.contrib import messages
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib.auth.models import User
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.db.models import (
    Avg,
    Q,
)
from django.http import (
    Http404,
    HttpResponseRedirect,
)
from django.http.response import (
    HttpResponse,
    HttpResponseNotFound,
)
from django.urls import reverse_lazy
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.views.generic import (
    <PERSON><PERSON><PERSON><PERSON>,
    Redirect<PERSON>iew,
    Template<PERSON>iew,
)
from rest_framework.renderers import <PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.reverse import reverse

from deepl.exceptions import AuthorizationException
from freezegun import freeze_time
from past.utils import old_div

from admin_customization.anomaly_checks import InvoiceNumbering
from admin_customization.forms import (
    DeeplTranslationForm,
    ImportGalleryForm,
    MassRandomFurnitureForm,
    RunFlowsWithTimeForm,
    ShortUrlAddForm,
)
from admin_customization.tasks import create_objects_from_json
from cstm_be.media_storage import private_media_storage
from custom import enums
from custom.ai.translations import translate_by_deepl
from custom.enums import LanguageEnum
from custom.models import GlobalSettings
from custom.utils.fpm_mass_generator import mass_generate_random_furniture
from custom.utils.url import tracking_utms_from_url
from gallery.enums import FurnitureCategory
from gallery.exceptions import NotCreatedAnyObjectsException
from gallery.models import (
    Jetty,
    Sotty,
    Watty,
)
from gallery.services.importer import ImportObjectsService
from invoice.models import Invoice
from kpi import kpis
from kpi.constants import (
    KPI_INTERVAL_DAILY,
    KPI_INTERVAL_MONTHLY,
    KPI_INTERVAL_WEEKLY,
)
from kpi.kpis import create_dynamic_segments_mixin
from kpi.tasks import task_regenerate_kpis_view
from kpi.utils import create_kpiview_cache_key
from mailing.flows.flow import (
    FailedPaymentBankTransferFlowByOrders,
    ProductionDelayFlow,
)
from mailing.flows.logistic_flow import (
    ComplaintReproductionShippedFlow,
    ProductShippedFlow,
    ProductToBeShippedFlow,
)
from mailing.tasks import mailing_flow_run
from orders.models import (
    Order,
    PaidOrders,
)
from payments.models import Notification
from producers.choices import ProductStatus
from producers.models import Product
from producers.production_system_utils.client import get_default_manufacturer_id
from producers.production_system_utils.enums import PSConnectionSettings
from regions.change_region import change_region
from regions.models import Region
from reviews.models import Review
from shortener.choices import RedirectChoices
from shortener.models import (
    RedirectedUserInfo,
    ShortUrl,
)
from taskapp.queue_status import CeleryQueueStatus
from user_profile.models import UserProfile

# TODO: refactor get_all_kpi_classes so those imports may be removed
# NOTE: Those imports even though not directly use in this module, are necessary
#       to initiate list of all KIPs by kpi.utils.get_all_kpi_classes function
from customer_service import kpis as customer_service_kpis  # noqa F401 isort:skip
from orders import kpis as orders_kpis  # noqa F401 isort:skip


class DashboardWelcomeView(TemplateView):
    template_name = 'admin_custom/dashboard.html'

    @method_decorator(cache_page(60 * 5), name='get_admin_dash_board')
    def get(self, request, *args, **kwargs):
        context = self.get_context_data(**kwargs)
        region = Region.get_region_for_language(LanguageEnum.DE)
        change_region(self.request.user.profile, region)

        context['orders'] = PaidOrders.objects.exclude(paid_at=None).order_by(
            '-paid_at'
        )
        context['orders_waiting_for_producer'] = (
            Product.objects.filter(batch_id=None)
            .filter(created_at__gt='2016-03-25')
            .filter(status=ProductStatus.NEW)
            .count()
        )
        context['customers'] = User.objects.filter(profile__user_type=2).order_by(
            '-id'
        )[:10]  # customer only
        context['producer_changelog'] = []
        context['ps_create'] = PSConnectionSettings.SERIALIZE

        context['wardrobes_in_last_7_days'] = (
            Product.objects.filter(
                cached_product_type='watty',
                id__gt=94149,
                created_at__gte=datetime.now() - timedelta(days=7),
            )
            .exclude(order__owner__is_staff=True)
            .count()
        )
        context['wardrobes_in_last_30_days'] = (
            Product.objects.filter(
                cached_product_type='watty',
                id__gt=94149,
                created_at__gte=datetime.now() - timedelta(days=30),
            )
            .exclude(order__owner__is_staff=True)
            .count()
        )

        context['items_in_cart'] = 'milions!'

        context['payment_notifications_ok'] = (
            Notification.objects.exclude(
                code='REPORT_AVAILABLE', transaction__isnull=True
            )
            .filter(success=True)
            .order_by('-event_date')[:10]
        )
        context['payment_notifications_error'] = (
            Notification.objects.filter(code='AUTHORISATION')
            .filter(success=False)
            .order_by('-event_date')[:10]
        )

        kpi_group = kpis.KPIMonthlyStandardGroup('Dashboard')
        kpi = kpis.KPIOrderTotalValue(kpi_group)
        kpi_time_segment_n = list(kpi.kpi_group.time_segments.segments.keys())[-1]

        kpi_group2 = kpis.KPIDailyGroup('Dashboard')
        kpi2 = kpis.KPIOrderTotalValue(kpi_group2)
        kpi_time_segment_n2 = list(kpi2.kpi_group.time_segments.segments.keys())[-1]

        actual_target = GlobalSettings.actual_month_target()
        now = datetime.now()

        context['order_total_value'] = round(
            old_div((kpi.get_value(kpi_time_segment_n).value), Decimal(1000)), 2
        )
        context['order_daily_value'] = round(
            old_div((kpi2.get_value(kpi_time_segment_n2).value), Decimal(1000)),
            2,
        )
        context['target_percentage'] = math.ceil(
            (old_div(context['order_total_value'], actual_target)) * 100
        )
        context['month_percentage'] = math.ceil(
            (now.day / float(calendar.monthrange(now.year, now.month)[1])) * 100
        )
        days_left = calendar.monthrange(now.year, now.month)[1] - (now.day - 1)
        context['target_per_day_left'] = round(
            old_div(
                (actual_target - context['order_total_value']),
                (days_left if days_left > 0 else 1),
            ),
            2,
        )
        context['was_per_day_left'] = round(
            old_div(
                (context['order_total_value']),
                ((calendar.monthrange(now.year, now.month)[1] - days_left + 1) or 1),
            ),
            2,
        )
        context['actual_target'] = actual_target
        context['review_all_score'] = Review.latest_objects.all().aggregate(
            Avg('score')
        )['score__avg']

        production_times = defaultdict(list)

        # some general info for desks
        for shelf_type in [
            enums.ShelfType.TYPE01,
            enums.ShelfType.TYPE02,
        ]:
            delivery_time_range = Jetty(
                shelf_type=shelf_type.value,
                shelf_category=FurnitureCategory.DESK.value,
            ).get_delivery_time_weeks_range()
            production_times[shelf_type.name].append(
                ['Desks', f"{delivery_time_range['min']}-{delivery_time_range['max']}"]
            )

        for shelf_type in [
            enums.ShelfType.TYPE01,
            enums.ShelfType.TYPE02,
            enums.ShelfType.VENEER_TYPE01,
        ]:
            delivery_matrix = Jetty(
                shelf_type=shelf_type.value
            ).get_delivery_time_summary()
            for color in shelf_type.colors.get_active_colors():
                for i, variant in enumerate(
                    ['', ' with doors', ' with drawers'],
                ):
                    week_range = delivery_matrix[i][color.value]
                    production_times[shelf_type.name].append(
                        [
                            '{}{}'.format(color.display_name, variant),
                            f'{week_range["min"]}-{week_range["max"]}',
                        ]
                    )
        # wardrobe is special case, as it use different model and hardcoded index
        for watty_type in [
            enums.ShelfType.TYPE03,
            enums.ShelfType.TYPE13,
            enums.ShelfType.VENEER_TYPE13,
            enums.ShelfType.TYPE23,
            enums.ShelfType.TYPE24,
            enums.ShelfType.TYPE25,
        ]:
            delivery_wardrobe_matrix = Watty(
                shelf_type=watty_type
            ).get_delivery_time_summary()
            for color in watty_type.colors.get_active_colors():
                i = 0
                week_range = delivery_wardrobe_matrix[i][color.value]
                production_times[f'Wardrobes - {watty_type.translated_name}'].append(
                    [
                        '{}'.format(color.display_name),
                        f'{week_range["min"]}-{week_range["max"]}',
                    ]
                )
        for sotty_type in [enums.ShelfType.SOFA_TYPE01]:
            delivery_wardrobe_matrix = Sotty().get_delivery_time_summary()
            for color in sotty_type.colors.get_active_colors():
                i = 0
                week_range = delivery_wardrobe_matrix[i][color.value]
                production_times[f'Sotty - {sotty_type.translated_name}'].append(
                    [
                        '{}'.format(color.display_name),
                        f'{week_range["min"]}-{week_range["max"]}',
                    ]
                )
        context['production_times'] = list(production_times.items())

        # version control info for environments
        context['git_branch'] = settings.GIT_BRANCH
        context['git_commit'] = settings.GIT_COMMIT

        return self.render_to_response(context=context)


class KPIsView(TemplateView):
    template_name = 'kpis/index.html'

    def prepare_response(self, request, **kwargs):
        response = None
        cache_key = create_kpiview_cache_key(request)

        groups = []

        board_name = kwargs.get('board_name', 'notfound')
        time_interval = kwargs.get('time_interval', KPI_INTERVAL_MONTHLY)

        context = {
            'regions': Region.objects.all().values_list('name', flat=True),
            'request': request,
            'utm_filter_url': reverse(
                'admin:kpis',
                kwargs={'board_name': 'utm', 'time_interval': time_interval},
            ),
        }

        if time_interval == KPI_INTERVAL_DAILY:
            group_class = kpis.KPIDailyGroup
        elif time_interval == KPI_INTERVAL_WEEKLY:
            group_class = kpis.KPIWeeklyStandardGroup
        else:
            group_class = kpis.KPIMonthlyStandardGroup

        region = None
        if 'region' in request.GET:
            try:
                region = Region.objects.get(name=request.GET['region'])
            except Region.DoesNotExist:
                pass

        if 'segments' in request.GET:
            current_time_segments = group_class('').time_segments
            time_span = int(
                request.GET.get('segments', current_time_segments.time_span)
            )
            dynamic_segments_mixin = create_dynamic_segments_mixin(
                current_time_segments.interval, time_span
            )

            class KPIDynamicGroup(dynamic_segments_mixin, group_class):
                pass

            group_class = KPIDynamicGroup

        params = dict(tracking_utms_from_url(unquote(request.get_full_path())))
        if 'csv_data' in request.GET and len(params) == 0:
            params = dict(tracking_utms_from_url(unquote(request.META['HTTP_REFERER'])))

        if board_name in ('bi',):
            group = group_class('BI Reports')
            kpis.KPIGASessions(group, region=region)
            kpis.KPIAdCost(group, region=region)
            kpis.KPIFreeAssemblyCost(group, region=region)
            kpis.KPIGAAddToCart(group, region=region)
            kpis.KPIOrderWithDiscountTotalValueGross(group, region=region)
            kpis.KPIOrderDiscountValueGross(group, region=region)
            kpis.KPIOrderTotalValue(group, region=region)
            kpis.KPIOrderAvgValue(group, region=region)
            kpis.KPIOrderFinished(group, region=region)
            kpis.KPIOrderFinishedPerDay(group, region=region)
            kpis.KPIOrderAvgItems(group, region=region)
            groups.append(group)

        if len(groups) == 0:
            return HttpResponseNotFound(
                content=(
                    "Couldn't find a KPI board with this name. Please check your URL."
                )
            )

        if 'csv' in request.GET:
            content = []
            for n, group in enumerate(groups):
                content.append(f'{group._name}\n')
                content.append(group.as_csv(with_header=n == 0))
            response = HttpResponse(''.join(content), content_type='text/csv')
            response['Content-Disposition'] = 'attachment; filename=kpis.csv'
        # TODO This is not pretty and needs refactoring.
        elif 'csv_data' in request.GET:
            csv_data = request.GET['csv_data']
            data_method = f'_get_{csv_data}'
            data = []
            if len(groups) > 0:
                group = groups[-1]
                if len(group.kpis) > 0:
                    kpi = group.kpis[0]
                    for time_segment_n in range(group.time_segments.time_span):
                        data += getattr(kpi, data_method)(time_segment_n)
            if len(data) > 0:
                csv_stream = io.StringIO()
                csv_writer = csv.writer(csv_stream)
                headers = []
                for field in type(data[0])._meta.fields:
                    headers.append(field.name)  # noqa: PERF401
                csv_writer.writerow(headers)
                for o in [
                    o
                    for o in set(data)
                    if type(o)
                    in (
                        Order,
                        UserProfile,
                    )
                ]:
                    row = []
                    for field in headers:
                        val = getattr(o, field)
                        if callable(val):
                            val = val()
                        row.append(val)
                    csv_writer.writerow(row)
                csv_stream.seek(0)
                response = HttpResponse(csv_stream.getvalue(), content_type='text/csv')
                response['Content-Disposition'] = (
                    f'attachment; filename=kpis_{board_name}_{csv_data}.csv'
                )
        elif 'json' in request.GET:
            kpi_groups_jsons = [kpi_group.as_json(as_dict=True) for kpi_group in groups]
            kpi_json = JSONRenderer().render(kpi_groups_jsons)
            response = HttpResponse(kpi_json, content_type='application/json')
        else:
            context['groups'] = groups
            response = self.render_to_response(context=context).render()

        if response:
            cache.set(cache_key, response, None)
            cache.set(f'{cache_key}_timestamp', int(time.time()), None)

        return response

    def get(self, request, *args, **kwargs):
        cache_key = create_kpiview_cache_key(request)
        response = cache.get(cache_key)
        is_regenerating = (
            settings.DEBUG
            or 'regenerating' in request.GET
            or 'csv' in request.GET
            or 'csv_data' in request.GET
            or 'segments' in request.GET
            or kwargs['board_name'] == 'campaigns'
            or kwargs['board_name'] == 'all'
        )
        if is_regenerating:
            response = self.prepare_response(request, **kwargs)
        elif not response:
            task_regenerate_kpis_view.delay(
                request.get_full_path(), notify_email=request.user.username
            )
            return self.render_to_response(context={'wait': True}).render()
        cache_timestamp = cache.get(f'{cache_key}_timestamp')
        cache_timestamp_dt = datetime.fromtimestamp(
            cache_timestamp if cache_timestamp else 0
        )
        if (datetime.now() - cache_timestamp_dt).seconds > 900 and not is_regenerating:
            task_regenerate_kpis_view.delay(request.get_full_path())
        return response


class MailingListView(TemplateView):
    template_name = 'admin_custom/mailing_list.html'

    def get(self, request, *args, **kwargs):
        context = self.get_context_data(**kwargs)
        mailings = self.get_mailing_templates()

        context['mailings'] = sorted(mailings)
        context['languages'] = LanguageEnum.values
        context['IS_PRODUCTION'] = settings.IS_PRODUCTION
        return self.render_to_response(context=context)

    def get_mailing_templates(self):
        mailings = []
        templates = inspect.getmembers(sys.modules['mailing.templates'])
        logistic_templates = inspect.getmembers(
            sys.modules['mailing.templates_logistic']
        )
        for name, obj in templates + logistic_templates:
            if inspect.isclass(obj) and hasattr(obj, 'context_vars'):
                mailings.append(name)
        return mailings


def process_flows_for_date(move_to_date):
    with freeze_time(move_to_date):
        mailing_flows = (
            ComplaintReproductionShippedFlow,
            FailedPaymentBankTransferFlowByOrders,
            ProductionDelayFlow,
            ProductShippedFlow,
            ProductToBeShippedFlow,
        )
        for mailing_flow in mailing_flows:
            # invoke regular function instead of celery task because celery
            # doesn't respond to forcing time freeze
            mailing_flow_run(mailing_flow.__name__)


class TestMailingFlowsView(TemplateView):
    template_name = 'admin_custom/test_flows.html'

    def get(self, request, *args, **kwargs):
        if settings.IS_PRODUCTION:
            raise Http404
        context = self.get_context_data(**kwargs)
        context['form'] = RunFlowsWithTimeForm()
        return self.render_to_response(context=context)

    def post(self, request, *args, **kwargs):
        if settings.IS_PRODUCTION:
            raise Http404
        data = request.POST.copy()
        context = self.get_context_data(**kwargs)
        action_form = RunFlowsWithTimeForm(data)

        if action_form.is_valid():
            process_flows_for_date(action_form.cleaned_data['run_flows_with_date'])
        context['form'] = RunFlowsWithTimeForm()
        return self.render_to_response(context)


def mailing_preview_view(request, mailing_name, language):
    try:
        clazz = getattr(sys.modules['mailing.templates'], mailing_name)
    except AttributeError:
        clazz = getattr(sys.modules['mailing.templates_logistic'], mailing_name)

    mail_obj = clazz('<EMAIL>', {})

    if request.GET.get('data'):
        data = {
            context_var: request.GET.get(context_var)
            for context_var in mail_obj.context_vars
        }
        mail_obj.data_html = data
        mail_obj.data_text = data
    else:
        mail_obj.set_test_data()

    html = mail_obj.render(language)['html']
    return HttpResponse(html)


def test_template(request, mailing_name, language):
    email = request.user.email or request.user.username
    try:
        clazz = getattr(sys.modules['mailing.templates'], mailing_name)
    except AttributeError:
        clazz = getattr(sys.modules['mailing.templates_logistic'], mailing_name)

    try:
        mail_obj = clazz(email, {})
        mail_obj.set_test_data()
        if hasattr(mail_obj, 'data_html') and 'order' in mail_obj.data_html:
            order = mail_obj.data_html['order']
            if hasattr(order, 'order_pretty_id'):
                mail_obj.topic_variables['order_pretty_id'] = order.order_pretty_id
        if 'topic_variables' in mail_obj.data_html:
            mail_obj.topic_variables = mail_obj.data_html['topic_variables']
        mail_obj.send(language=language)
    except Exception as e:
        return HttpResponse(f'Unable to send mails due to missing email address. {e!s}')
    return HttpResponse('Test mails sent successfully')


class RevPerDayView(TemplateView):
    template_name = 'admin_custom/report_rev_per_day.html'

    def get(self, request, *args, **kwargs):
        context = self.get_context_data(**kwargs)
        orders = Order.paid_orders.values_list('created_at', 'total_price', 'country')
        orders_and_dates = []
        orders_and_months = []
        orders = sorted(orders, key=lambda x: x[0])
        for key, values in groupby(orders, key=lambda row: row[0].date()):
            values = list(values)
            country_dict = {}
            for value in values:
                country = str(value[2]).lower()
                if country in {'poland', 'polska', 'pl'}:
                    country = 'poland'
                if country in {'unitedkingdom', 'united_kingdom', 'uk'}:
                    country = 'unitedkingdom'
                if country in {'au', 'austria'}:
                    country = 'austria'
                if country in {'none', '', None}:
                    country = 'not set'
                if country not in country_dict:
                    country_dict[country] = (0, 0)
                if value[1] is not None:
                    country_dict[country] = (
                        country_dict[country][0] + value[1],
                        country_dict[country][1] + 1,
                    )
            orders_and_dates.append(
                [
                    key,
                    sum([x[1] for x in values if x[1] is not None]),  # noqa: C419
                    len(values),
                    country_dict,
                ]
            )
        context['orders_and_dates'] = orders_and_dates
        for key, values in groupby(
            orders_and_dates, key=lambda x: '%s-%s' % (x[0].year, x[0].month)
        ):
            values = list(values)
            country_dict = {}
            for value in values:
                for country, entry_data in list(value[3].items()):
                    if country not in country_dict:
                        country_dict[country] = (0, 0)
                    if value[1] is not None:
                        country_dict[country] = (
                            country_dict[country][0] + entry_data[0],
                            country_dict[country][1] + entry_data[1],
                        )
            orders_and_months.append(
                [
                    key,
                    sum([x[1] for x in values]),  # noqa: C419
                    sum([x[2] for x in values]),  # noqa: C419
                    country_dict,
                ]
            )
        context['orders_and_months'] = orders_and_months
        return self.render_to_response(context=context)


class AnomalyChecksView(TemplateView):
    template_name = 'admin_custom/anomaly_checks.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['invoice_anomalies'] = InvoiceNumbering().check()
        return context


class AdminSearchView(TemplateView):
    template_name = 'admin_custom/search.html'

    @staticmethod
    def normalize_query(
        query_string,
        findterms=re.compile(r'"([^"]+)"|(\S+)').findall,
        normspace=re.compile(r'\s{2,}').sub,
    ):
        return [normspace('', (t[0] or t[1]).strip()) for t in findterms(query_string)]

    def get_query(self, query_string, search_fields):
        query = None
        terms = self.normalize_query(query_string)
        for term in terms:
            or_query = None
            for field_name in search_fields:
                q = Q(**{f'{field_name}__icontains': term})
                if or_query is None:
                    or_query = q
                else:
                    or_query = or_query | q
            if query is None:
                query = or_query
            else:
                query = query & or_query
        return query

    def get(self, request, *args, **kwargs):
        results = OrderedDict(
            [(t, []) for t in ('orders', 'users', 'products', 'invoices')]
        )
        query_string = ''

        if ('q_product' in request.GET) and request.GET['q_product'].strip():
            query_string = request.GET['q_product']
            product_entry_query = self.get_query(query_string, ['id', 'order__id'])
            results['products'] = Product.objects.filter(product_entry_query)

        if ('q_invoice' in request.GET) and request.GET['q_invoice'].strip():
            query_string = request.GET['q_invoice']
            invoice_entry_query = self.get_query(query_string, ['id', 'order__id'])
            results['invoices'] = Invoice.objects.filter(invoice_entry_query)

        if ('q_order' in request.GET) and request.GET['q_order'].strip():
            query_string = request.GET['q_order']
            order_entry_query = self.get_query(
                query_string,
                [
                    'first_name',
                    'last_name',
                    'company_name',
                    'invoice_first_name',
                    'invoice_last_name',
                    'invoice_company_name',
                    'email',
                    'invoice_email',
                    'order_pretty_id',
                    'id',
                    'owner__username',
                ],
            )
            results['orders'] = Order.objects.filter(order_entry_query)

        if ('q_user' in request.GET) and request.GET['q_user'].strip():
            query_string = request.GET['q_user']
            user_entry_query = self.get_query(
                query_string,
                [
                    'first_name',
                    'last_name',
                    'company_name',
                    'email',
                    'id',
                    'user__username',
                ],
            )
            results['users'] = UserProfile.objects.filter(user_entry_query)

        return self.render_to_response(
            context={'query': query_string, 'results': results}
        )


class ShortUrlAddView(TemplateView):
    form_class = ShortUrlAddForm
    success_url = reverse_lazy('admin:shorturl_add')
    template_name = 'admin_custom/shorturl.html'

    def get(self, request, *args, **kwargs):
        return self.render_to_response(
            context={
                'active_shorturls': ShortUrl.objects.count(),
                'hits': RedirectedUserInfo.objects.count(),
                'form': self.form_class(),
            }
        )

    def post(self, request, *args, **kwargs):
        form = self.form_class(request.POST)
        context = {
            'active_shorturls': ShortUrl.objects.count(),
            'hits': RedirectedUserInfo.objects.count(),
            'form': form,
            'shorturls': [],
        }
        if form.is_valid():
            name = form.cleaned_data['name']
            description = form.cleaned_data['description']
            url = form.cleaned_data['url']
            try:
                context['shorturls'] = [
                    ShortUrl.objects.create(
                        name=f'{t[0]}_{name}',
                        description=description,
                        url=(
                            f'{url}?utm_source={t[1]}&utm_medium=post&'
                            f'utm_campaign=journal_{name}'
                        ),
                        redirect_type=RedirectChoices.PERMANENT,
                    )
                    for t in (('fb', 'facebook'), ('ig', 'instagram'))
                ]
            except IntegrityError:
                messages.error(request, f'Short url {name} already exists!')
            messages.success(request, f'Successfully added {name} short url.')
        return self.render_to_response(context)


class JsonProductionView(RedirectView):
    def get_redirect_url(self, *args, **kwargs):
        shelf_id = self.kwargs.get('shelf_id')
        mode = self.kwargs.get('mode')

        product = Product.objects.get(pk=shelf_id)
        gallery_hash = hashlib.md5(  # noqa: S324
            str(product.order_item_serialized['id']).encode(),
        ).hexdigest()

        base_url = PSConnectionSettings.REDIRECT_URL
        redirect_url = '{0}/products/preview_cstm/{1}/{2}'.format(
            base_url,
            product.id,
            gallery_hash,
        )
        if mode == 'recalculate':
            manufactor_id = product.manufactor_id or get_default_manufacturer_id(
                product.order_item.order_item.shelf_type,
                product.cached_material,
            )
            redirect_url = f'{redirect_url}/{manufactor_id}/'

        return redirect_url


class QueueStatusView(TemplateView):
    template_name = 'admin_custom/queue_status.html'

    def get(self, request, *args, **kwargs):
        context = self.get_context_data(**kwargs)

        celery_queue = CeleryQueueStatus()

        context.update(
            {
                'celery_apps': celery_queue.get_app_names(),
                'celery_tasks_count': celery_queue.get_tasks_count(),
                'celery_most_common_tasks': celery_queue.get_most_common_tasks(),
                'celery_head_tasks': celery_queue.get_head_tasks_names(),
                'celery_tail_tasks': celery_queue.get_tail_tasks_names(),
            },
        )
        return self.render_to_response(context=context)


@method_decorator(staff_member_required, name='dispatch')
class ImportJettyWattyRedirectView(RedirectView):
    def get_redirect_url(self, *args, **kwargs):
        return reverse('admin:create_objects')


@method_decorator(staff_member_required, name='dispatch')
class ImportObjectsView(FormView):
    form_class = ImportGalleryForm
    redirect_url_gallery = ''
    template_name = 'admin_custom/create_gallery_objects.html'

    def form_valid(self, form):
        file = self.request.FILES.get('file')
        form_process_to_production = form.cleaned_data['process_to_production']
        is_influencers = form.cleaned_data['is_influencers']

        json_data_from_file = json.load(file)
        service = ImportObjectsService(
            json_data=json_data_from_file,
            owner_id=self.request.user.id,
        )
        process_to_production = form_process_to_production or service.is_complaint

        redirect_url = self.get_redirect_url(
            gallery_type=service.gallery_type,
            process_to_production=process_to_production,
            is_complaint=service.is_complaint,
        )

        if service.objects_count > 5 and process_to_production:
            return self.create_objects_asynchronous(
                file=file,
                process_to_production=process_to_production,
                is_influencers=is_influencers,
                redirect_url=redirect_url,
                owner_id=self.request.user.id,
            )
        else:
            return self.create_objects_synchronous(
                service=service,
                process_to_production=process_to_production,
                is_influencers=is_influencers,
                form=form,
                redirect_url=redirect_url,
            )

    def create_objects_asynchronous(
        self,
        file,
        process_to_production,
        is_influencers,
        owner_id,
        redirect_url,
    ):
        file_name = os.path.join('tmp', file.name.replace(' ', '_'))
        file_path = private_media_storage.save(file_name, file)
        create_objects_from_json.delay(
            file_path=file_path,
            owner_id=owner_id,
            process_to_production=process_to_production,
            is_influencers=is_influencers,
        )
        messages.add_message(
            self.request,
            level=messages.INFO,
            message='Objects are loading in background, please wait few minutes',
        )
        return HttpResponseRedirect(redirect_url)

    def create_objects_synchronous(
        self,
        service,
        process_to_production,
        is_influencers,
        form,
        redirect_url,
    ):
        try:
            objects_ids = service.create_objects_from_json(
                process_to_production, is_influencers
            )
        except ValidationError as exc:
            form.add_error('file', exc)
            return self.form_invalid(form)
        except NotCreatedAnyObjectsException:
            form.add_error('file', 'Did not create any object for this file')
            return self.form_invalid(form)

        created_objects_ids = ','.join(str(id) for id in objects_ids)
        messages.add_message(
            self.request,
            messages.INFO,
            f'Created objects id: {created_objects_ids}',
        )
        return HttpResponseRedirect(redirect_url.format(created_objects_ids))

    @staticmethod
    def get_redirect_url(gallery_type, process_to_production, is_complaint):
        if is_complaint:
            reverse_url = reverse('admin:producers_productcomplaint_changelist')
        else:
            if process_to_production:
                reverse_url = reverse('admin:producers_product_changelist')
            else:
                reverse_url = reverse(f'admin:gallery_{gallery_type}_changelist')
        return reverse_url + '?id__in={}'


class OffscreenRendererView(TemplateView):
    template_name = 'admin_custom/offscreen_renderer.html'

    def post(self, request, *args, **kwargs):
        context = self.get_context_data(**kwargs)
        geometry = request.POST.get('geometry')
        furniture_type = request.POST.get('furniture_type')
        context.update(
            {
                'geometry': geometry,
                'furnitureType': furniture_type,
            }
        )
        return self.render_to_response(context)


class DeeplTranslationView(FormView):
    template_name = 'admin_custom/deepl_translate.html'
    form_class = DeeplTranslationForm

    def form_valid(self, form):
        original = form.cleaned_data['phrase_in_english']
        try:
            translated = translate_by_deepl(text=original, language=LanguageEnum.EN)
        except AuthorizationException:
            messages.error(
                self.request,
                'Authorization error. Please check your DeepL API key.',
            )
            return self.form_invalid(form)
        context = self.get_context_data()
        return self.render_to_response(context={'results': translated, **context})


class MassRandomFurnitureCreator(FormView):
    form_class = MassRandomFurnitureForm
    template_name = 'admin_custom/mass_furniture_generator.html'
    success_url = reverse_lazy('admin:generate_furniture')

    def form_valid(self, form):
        mass_generate_random_furniture.delay(
            n=form.cleaned_data['quantity'],
            user_id=self.request.user.id,
        )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        target = cache.get('random_furniture_target')
        if target:
            progress = cache.get('random_furniture_progress', 0)
            context.update({'target': target, 'progress': progress})
        return context
