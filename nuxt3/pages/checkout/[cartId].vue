<template>
  <div ref="checkoutWrapper">
    <Head>
      <Title>{{ $t('checkout.meta.title') }}</Title>
      <Meta
        name="og:title"
        hid="og:title"
        :content="$t('checkout.meta.title')"
      />
      <Meta
        name="description"
        hid="description"
        :content="$t('checkout.meta.description')"
      />
      <Meta
        name="og:description"
        hid="og:description"
        :content="$t('checkout.meta.description')"
      />
    </Head>
    <CheckoutMobileSummaryBarNew v-if="!isDesktopViewport" />
    <ClientOnly>
      <Teleport to="#checkout-teleports">
        <div class="lg:hidden p-16 border-t border-neutral-400 rounded-t-12 bg-white">
          <div class="lg:hidden">
            <ScartSimpleOption
              v-if="totalPrice"
              class="mb-8 !py-0"
              v-bind="{
                title: $t('checkout.vat.included_headline'),
                value: totalPrice,
                leftStyles: 'block semibold-16 text-neutral-900',
                rightStyles: 'block semibold-16 text-neutral-900'
              }"
            />
            <ScartSimpleOption
              class="!py-0 mb-8"
              v-bind="{
                title: $t('checkout.delivery.costs_info'),
                value: format(deliveryPrice),
                leftStyles: 'block normal-12 text-neutral-900',
                rightStyles: 'block normal-12 text-neutral-900'
              }"
            />
          </div>
          <template v-if="displayPaymentButton">
            <CheckoutPaymentButton
              v-if="!isDesktopViewport && paymentButtonLoader"
            />
            <div
              v-else-if="!isDesktopViewport && !paymentButtonLoader"
              class="relative h-48"
            >
              <UiDotsLoader
                bounce-class="bg-orange !w-16 !h-16 !mr-8"
              />
            </div>
          </template>
        </div>
      </Teleport>
    </ClientOnly>
    <CheckoutFrameNew
      heading-id="delivery"
      :heading-text="!isOtherRegion ? `${$t('scart.checkout')} <span class='normal-14 text-neutral-700' data-testid='checkout-items-count'>(${cartItemsCount} ${$t('common.items')})</span>` : $t('checkout.other_region.headline')"
    >
      <template #breadcrumbs>
        <CheckoutBreadcrumbsNew
          class="!mb-[29px]"
          v-bind="{
            eventLabel: 'address',
            activeIndex: 1
          }"
        />
      </template>
      <template #default>
        <ClientOnly>
          <CheckoutOtherRegion
            v-if="isOtherRegion"
            class="bg-white"
          />
          <CheckoutFormNew
            v-else
          />
        </ClientOnly>
      </template>
      <template #summary>
        <ClientOnly>
          <CheckoutStickyScroll>
            <template v-if="isDesktopViewport">
              <CheckoutSummaryNew
                class="bg-white p-32"
                v-bind="{
                  pending: !formIsMounted,
                  displayPaymentButton,
                  displayAssemblyPrice: true,
                  displayNewsletter: true,
                  displayAssembly: false,
                  displayDeliveryNotice: hasDeliveryTimeGap
                }"
              />
            </template>
          </CheckoutStickyScroll>
        </ClientOnly>
      </template>
    </CheckoutFrameNew>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useGlobal } from '~/stores/global';
import { useScartStore } from '~/stores/scart';
import { checkoutAnalytics } from '~/composables/checkout/checkoutAnalytics';

import useMq from '~/composables/useMq';
import usePrice from '~/composables/usePrice';
import useCartStatus from '~/composables/useCartStatus';
import handleBFCache from '~/composables/checkout/handleBFCache';

definePageMeta({
  key: () => {
    const route = useRoute();
    return `checkout${route.params?.orderId}`;
  },
  layout: 'checkout',
  middleware: ['auth', 'checkout-status-form']
});

const gtm = useGtm();
const route = useRoute();
const global = useGlobal();
const cart = useScartStore();
const { deliveryPrice } = storeToRefs(cart);
const { format } = usePrice();
const { isDesktopViewport } = useMq('lg');
const { fetchUserStatus } = useCartStatus();
const { paymentButtonLoader, displayPaymentButton } = useAdyenDropin();
const { checkout2025Event } = checkoutAnalytics();

const { formIsMounted } = storeToRefs(useCheckoutStore());
const { cartId: storeCartId, cartItemsCount } = storeToRefs(global);

const totalPrice = computed(() => format(cart.totalPrice));
const isOtherRegion = computed(() => global.regionName === '_other');

const cartId = route.params.cartId || storeCartId.value;

handleBFCache();

await fetchUserStatus(cartId as string | null);

const hasDeliveryTimeGap = computed(() => {
  const deliveryTimes = cart.cartItems
    .filter(item => item.content_type !== 'samplebox')
    .map(item => parseInt(item.delivery_time))
    .filter(time => !isNaN(time));

  return deliveryTimes.some((time, index) => deliveryTimes.slice(index + 1).some(laterTime => Math.abs(time - laterTime) > 2));
});

onMounted(() => {
  gtm?.push({ ecommerce: null });
  gtm?.push(checkout2025Event('begin_checkout'));
});
</script>
