<template>
  <main class="md:min-h-screen w-full pt-24 lg:pt-40 lg2:pb-64 lg:bg-beige-100">
    <div class="grid-container mx-auto">
      <slot name="breadcrumbs" />
      <h2
        v-if="!isLoading && headingId && headingText"
        v-bind:id="headingId"
        class="bold-24 text-neutral-900 mb-32 lg:mb-24 lg-max:hidden"
        v-html="headingText"
      />
      <div
        class="min-h-screen lg:grid lg:grid-cols-12 lg:gap-x-24 lg:relative lg:items-start"
        v-bind:class="{ 'md-max:flex md-max:flex-col-reverse md-max:justify-end': mobileSwitchColumns }"
      >
        <div class="lg:col-span-7 lg2:col-span-8 md-max:bg-beige-100 md-max:-mx-16">
          <slot name="address" />
          <ClientOnly>
            <slot />
            <template #placeholder>
              <div class="flex min-h-[calc(100vh)] relative lg:grid lg:grid-cols-8 md-max:bg-white">
                <div class="w-full relative lg:col-span-1 lg:col-start-7">
                  <UiDotsLoader
                    class="mt-2 !top-[100px] w-full flex justify-center"
                    bounce-class="bg-orange !w-20 !h-20 !mr-8"
                  />
                </div>
              </div>
            </template>
          </ClientOnly>
        </div>
        <div class="lg:top-[101px] lg:col-span-5 lg2:col-span-4 h-full">
          <slot name="summary" />
        </div>
      </div>
    </div>
  </main>
</template>

<script setup lang="ts">
defineProps({
  headingId: {
    type: [String, Boolean],
    default: false
  },
  headingText: {
    type: [String, Boolean],
    default: false
  },
  mobileSwitchColumns: {
    type: Boolean,
    default: false
  }
});

const isLoading = ref(true);

onMounted(() => {
  isLoading.value = false;
});

</script>
