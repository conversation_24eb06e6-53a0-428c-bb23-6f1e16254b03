<template>
  <div>
    <Transition name="fade">
      <div
        v-show="formLoaded && isNewFormMounted"
      >
        <FormKit
          v-if="formDisplay"
          id="myForm"
          v-model="formValues"
          type="form"
          :actions="false"
          v-on:submit="submitForm"
          v-on:submit-invalid="failedValidation"
        >
          <CheckoutFormSection
            class="mb-12"
            data-testid="delivery-address-section"
          >
            <h2 class="semibold-20 md:semibold-28 text-neutral-900 mb-24">
              {{ $t('checkout.summary.headline') }}
            </h2>
            <div class="md:flex md:gap-16 md:mb-16">
              <FormKit
                type="tyText"
                name="firstName"
                :validation="'required'"
                autocomplete="given-name"
                :label="$t('checkout.form.first_name.label')"
                outer-class="md-max:mb-16 md:w-full"
                :validation-messages="{
                  required: $t('checkout.form.first_name.required'),
                }"
              />
              <FormKit
                type="tyText"
                name="lastName"
                validation="required"
                autocomplete="family-name"
                :label="$t('checkout.form.last_name.label')"
                outer-class="md-max:mb-16 md:w-full"
                :validation-messages="{
                  required: $t('checkout.form.last_name.required'),
                }"
              />
            </div>
            <button
              class="bg-beige-100 w-full rounded-8 pl-[44px] pr-12 py-16 text-left relative normal-16 text-neutral-900 mb-16 border border-beige-200"
              data-testid="address-validation-button"
              v-on:click.prevent="addressModal = true"
            >
              <IconSearch
                class="absolute top-1/2 left-12 transform -translate-y-1/2 "
              />
              {{ $t('checkout_address_input_placeholder') }}
            </button>
            <FormKit
              outer-class="mb-16"
              type="tyText"
              name="streetAddress1"
              autocomplete="street-address"
              validation="required"
              :validation-messages="{
                required: $t('checkout.form.street_name.required'),
              }"
              :label="$t('checkout.form.street_name.label')"
            />
            <FormKit
              type="tyText"
              outer-class="my-16"
              name="streetAddress2"
              :label="$t('checkout.form.street_address_2.label')"
            />
            <FormKit
              type="tySelect"
              name="floorNumber"
              outer-class="mb-16"
              :label="$t('checkout.form.floor_number.label')"
              :options="[
                { value: 0, label: $t('checkout.dropdown.answer.floor.0.house') },
                { value: 1, label: '1' },
                { value: 2, label: '2' },
                { value: 3, label: '3' },
                { value: 4, label: '4' },
                { value: 5, label: $t('checkout.form.floor_number.options.5') }
              ]"
              :placeholder="$t('checkout.form.floor_number.placeholder')"
              v-on:input="handleFloorOption"
              v-on:click="handleFloorSelect"
            />
            <template v-if="formValues.floorNumber && formValues.floorNumber > 0">
              <p
                class="normal-16"
                v-html="$t('checkout.form.elevator.label')"
              />
              <FormKit
                type="radio"
                name="noElevator"
                outer-class="mt-16 mb-32 elevator-radio"
                :options="{
                  yes: $t('checkout.form.elevator.options.yes'),
                  no: $t('checkout.form.elevator.options.no'),
                }"
              />
            </template>
            <div class="flex gap-16 mb-16">
              <FormKit
                type="tyText"
                name="postalCode"
                autocomplete="postal-code"
                validation="required|isDeliveryAvailable"
                :validation-rules="{ isDeliveryAvailable: async ({ value }) => await validatePostalCodeDelivery(value as string) }"
                :validation-messages="{
                  required: $t('checkout.form.postal_code.required'),
                  isDeliveryAvailable: $t('checkout.form.postal_code.delivery_not_available'),
                }"
                maxlength="20"
                :label="$t('checkout.form.postal_code.label')"
                outer-class="w-2/5 md:w-full"
              />
              <FormKit
                type="tyText"
                name="city"
                validation="required"
                :validation-messages="{
                  required: $t('checkout.form.city.required'),
                }"
                :label="$t('checkout.form.city.label')"
                outer-class="w-3/5 md:w-full"
              />
            </div>

            <CheckoutInputTooltipWrapper
              v-bind="{
                disableTooltip: !cartStore.hasT03 || !global.hasS01 || !global.hasCorduroy,
              }"
            >
              <template #default>
                <InputSelectChangeCountryNew
                  v-bind="{
                    label: $t('checkout.billing.dropdown.label.country'),
                    placeholder: $t('checkout.choose_your_country_palceholder'),
                    name: 'region',
                    validation:'required',
                    disabled: cartStore.hasT03 || global.hasS01 || global.hasCorduroy,
                    region: formValues.region
                  }"
                />
              </template>
              <template #tooltipBody>
                <p v-html="$t('checkout.select_region_tooltip')" />
              </template>
            </CheckoutInputTooltipWrapper>
            <FormKit
              type="tyTextArea"
              name="notes"
              outer-class="w-full mt-16"
              :label="$t('checkout.billing.delivery_instructions.label')"
              :maxlength="additionalInfoMaxLength"
            />
            <p
              v-if="formValues.notes"
              class="mt-4 text-right normal-14"
            >
              {{ formValues.notes.length }} / {{ additionalInfoMaxLength }}
            </p>
            <p class="normal-14 text-neutral-900 flex justify-start items-start mt-8">
              <span class="mr-8">
                <IconInfo class="w-24 h-24" />
              </span>
              <span class="pt-2">
                {{ $t('checkout.courier.additional.info_info') }}
              </span>
            </p>

            <h2 class="mt-32 semibold-20 md:semibold-28 text-neutral-900 mb-24">
              {{ $t('account.billing_address') }}
            </h2>

            <FormKit
              type="tyBox"
              name="sameAsDelivery"
              :label="$t('checkout.billing.same_as_delivery.label')"
              outer-class="mt-16 h-[20px]"
              label-class="pl-16 cursor-pointer normal-16"
            />

            <div
              v-show="!formValues.sameAsDelivery"
              class="pt-[36px]"
            >
              <FormKit
                v-model="B2B"
                :options="{
                  regular: $t('checkout.form.client_tab.private'),
                  B2B: $t('checkout.form.client_tab.company')
                }"
                type="radio"
                outer-class="radio-b2b mb-32"
              />
              <div v-show="vatNumberAndCompanyNameVisible">
                <FormKit
                  type="tyText"
                  name="vat"
                  validation="required"
                  outer-class="mb-16"
                  :validation-rules="{
                    required: vatNumberAndCompanyNameVisible,
                  }"
                  :validation-messages="{
                    required: $t('delivery_time_frames.choose_dates_modal.error_field_required'),
                  }"
                  :placeholder="$t('checkout.form.vat.label')"
                  :label="$t('checkout.form.vat.label')"
                  autocomplete="on"
                />
                <FormKit
                  type="tyText"
                  name="invoiceCompanyName"
                  autocomplete="organization"
                  outer-class="mb-16"
                  :label="$t('checkout.form.company_name.label')"
                />
              </div>
              <div class="md:flex md:gap-16 md:mb-16">
                <FormKit
                  type="tyText"
                  name="invoiceFirstName"
                  outer-class="md-max:mb-16 md:w-full"
                  :validation="!formValues.sameAsDelivery ? 'required': ''"
                  validation-behavior="live"
                  autocomplete="given-name"
                  :label="$t('checkout.form.first_name.label')"
                  :validation-messages="{
                    required: $t('checkout.form.first_name.required'),
                  }"
                />
                <FormKit
                  type="tyText"
                  name="invoiceLastName"
                  outer-class="md-max:mb-16 md:w-full"
                  :validation="!formValues.sameAsDelivery ? 'required' : ''"
                  validation-behavior="live"
                  autocomplete="family-name"
                  :label="$t('checkout.form.last_name.label')"
                  :validation-messages="{
                    required: $t('checkout.form.last_name.required'),
                  }"
                />
              </div>
              <FormKit
                type="tyText"
                name="invoiceStreetAddress1"
                outer-class="mb-16"
                autocomplete="street-address"
                :validation="!formValues.sameAsDelivery ? 'required' : ''"
                validation-behavior="live"
                :label="$t('checkout.form.street_address.label')"
                :validation-messages="{
                  required: $t('checkout.form.street_address.required'),
                }"
              />
              <div class="md:flex md:gap-16 md:mb-16">
                <FormKit
                  type="tyText"
                  name="invoicePostalCode"
                  outer-class="md-max:mb-16 md:w-full"
                  :validation="!formValues.sameAsDelivery ? 'required' : ''"
                  autocomplete="postal-code"
                  maxlength="20"
                  validation-behavior="live"
                  :label="$t('checkout.form.postal_code.label')"
                  :validation-messages="{
                    required: $t('checkout.form.postal_code.required'),
                  }"
                />
                <FormKit
                  type="tyText"
                  name="invoiceCity"
                  outer-class="md-max:mb-16 md:w-full"
                  :validation="!formValues.sameAsDelivery ? 'required' : ''"
                  validation-behavior="live"
                  :label="$t('checkout.form.city.label')"
                  :validation-messages="{
                    required: $t('checkout.form.city.required'),
                  }"
                />
              </div>
            </div>
            <div
              v-show="!formValues.sameAsDelivery"
            >
              <InputSelectChangeCountryNew

                outer-class="mb-32 md:mb-[72px]"
                v-bind="{
                  label: $t('checkout.billing.dropdown.label.country'),
                  placeholder: $t('checkout.choose_your_country_palceholder'),
                  name: 'invoiceCountry',
                  validation:'required',
                  region: formValues.invoiceCountry ? formValues.invoiceCountry : formValues.region
                }"
              />
            </div>
          </CheckoutFormSection>

          <CheckoutFormSection data-testid="contact-section">
            <h2
              id="contact"
              class="mb-16 semibold-20 md:semibold-28 text-offblack-800"
              v-html="$t('checkout.contact_details.headline')"
            />

            <FormKit
              type="tyText"
              input-type="email"
              name="email"
              outer-class="mb-16"
              autocomplete="on"
              :label="$t('checkout.form.email.label')"
              :validation="!isSignedIn && formValues.signup ? 'required|email|isEmailAlreadyRegistered' : 'required|email'"
              :validation-rules="{ isEmailAlreadyRegistered: async ({ value }) => await isEmailAlreadyRegistered(value as string) }"
              :validation-messages="{
                required: $t('checkout.form.email.required_nuxt3'),
                email: $t('checkout.form.email.required_nuxt3'),
                isEmailAlreadyRegistered: $t('checkout.form.email.already_registered_error'),
              }"
            />
            <div class="flex mb-16 md:mb-8">
              <CheckoutPhonePrefixSelect
                v-model="phonePrefixCode"
                label="code"
              />
              <FormKit
                type="tyNumber"
                input-type="number"
                name="phone"
                validation="required"
                :label="$t('checkout.form.phone_number.label')"
                :validation-messages="{
                  required: $t('checkout.form.phone_number.required'),
                }"
                outer-class="w-full ml-16"
                input-class="appearance-none"
                autocomplete="tel-national"
              />
            </div>
            <p class="normal-14 text-neutral-900 flex justify-start items-start">
              <span class="mr-8">
                <IconInfo class="w-24 h-24" />
              </span>
              <span class="pt-2">
                {{ $t('checkout_phone_number_info') }}
              </span>
            </p>
            <template v-if="!isSignedIn">
              <FormKit
                type="tyBox"
                name="signup"
                :label="'Sign up for an account after making a purchase.'"
                outer-class="mt-48 h-[20px]"
                label-class="cursor-pointer normal-16"
              />
              <FormKit
                v-if="formValues.signup"
                outer-class="mt-32"
                type="tyPassword"
                name="password"
                data-testid="input-login-password"
                autocomplete="current-password"
                autocapitalize="off"
                :validation="!isSignedIn && formValues.signup ? '*+length:8|*+hasUpperAndLowerCase|*+hasNumbersOrSymbols|*hasNoSpaces' : ''"
                validation-hints="true"
                v-bind="{
                  label: $t('account.enter_password'),
                  validationMessages:{
                    length: $t('account.register.validation.length_hint'),
                    hasUpperAndLowerCase: $t('account.register.validation.case_hint'),
                    hasNumbersOrSymbols: $t('account.register.validation.contains_hint'),
                    hasNoSpaces: $t('account.register.validation.spaces'),
                  }
                }"
              />
            </template>
          </CheckoutFormSection>
        </FormKit>
        <CheckoutFormSection
          v-if="formDisplay"
          class="mt-12"
          data-testid="assembly-section"
        >
          <h2
            class="mb-16 semibold-20 md:semibold-28 text-offblack-800"
            v-html="$t('checkout_delivery_methods_title')"
          />
          <CheckoutNewDeliveryMethods v-if="deliveries.length" />
        </CheckoutFormSection>
        <CheckoutFormSection
          v-if="hasS01 && oldSofaCollection"
          class="mt-12"
          data-testid="premium-services-section"
        >
          <h2
            class="mb-16 semibold-20 md:semibold-28 text-offblack-800"
            v-html="$t('checkout_services_premium_title')"
          />
          <button
            class="premium-services-item"
            :class="{ 'active': oldSofaCollection.active }"
            v-on:click="handleSofaRecycling"
          >
            <span class="flex items-center justify-center">
              <div class="w-full">
                <p class="bg-neutral-900 text-white inline px-8 py-2 rounded-4 normal-12">
                  {{ $t('checkout.recommended_label') }}
                </p>
                <p class="mt-8 semibold-16 text-neutral-900">
                  {{ $t('checkout_services_old_sofa_recycling_headline') }}
                </p>
                <p class="mt-2 text-neutral-700 normal-12">
                  {{ $t('checkout_services_old_sofa_recycling_body') }}
                </p>
              </div>
              <p
                class="normal-16 text-neutral-900"
                data-testid="old-sofa-collection-price"
              >
                {{ format(oldSofaCollection.price) }}
              </p>
            </span>
          </button>
        </CheckoutFormSection>
        <CheckoutFormSection
          v-if="!disablePayments"
          data-testid="payment-section"
          :class="{ 'mt-12': formDisplay }"
        >
          <div class="flex justify-between items-center w-full mb-16">
            <h2
              class="semibold-20 md:semibold-28 text-offblack-800"
              v-html="$t('checkout.payment_methods.headline')"
            />
            <p class="flex justify-start items-center">
              <IconLocker class="mr-4" />
              <span class="text-success-500 normal-14">{{ $t('cart.header.secure_checkout') }}</span>
            </p>
          </div>

          <CheckoutPayments
            v-if="shouldLoadPaymentsOnCheckout && orderId && isNewFormMounted"
            v-bind="{ formValues }"
          />
        </CheckoutFormSection>
        <CheckoutSummaryMobile
          v-if="!isDesktopViewport"
          data-testid="mobile-newsletter-terms-section"
          v-bind="{
            displayNewsletter: true,
            displayTerms: true
          }"
        />

        <CheckoutSwitchRegionModal
          v-model="checkoutSwitchRegionModal"
          :default-close="true"
          v-on:on-success="onSuccess"
        />
      </div>
    </Transition>
    <LazyModalAddressAutocomplete
      v-model="addressModal"
      v-on:dispatch-address="dispatchAddress"
    />
  </div>
</template>

<script setup lang="ts">
import { getValidationMessages } from '@formkit/validation';
import { useDebounceFn } from '@vueuse/core';

import { useToast } from 'vue-toastification';
import { checkoutRegions } from '~/composables/checkout/regions';
import { handleFormValues } from '~/composables/checkout/handleFormValues';
import scrollToElement from '~/helpers/scrollToElement';
import { useScartStore } from '~/stores/scart';
import { CHECK_IS_ACCOUNT_EXIST } from '~/api/account';
import {
  CHANGE_OLD_SOFA_COLLECTION
} from '~/api/checkout';
const { $i18n, $logException } = useNuxtApp();

const route = useRoute();

const { format } = usePrice();

const global = useGlobal();
const cartStore = useScartStore();
const { fetchUserStatus, fetchDeliveries } = useCartStatus();

const { cartId, isSignedIn, oldSofaCollectionAvailable, hasS01, doorstepSottyDelivery } = storeToRefs(global);
const { oldSofaCollectionPrice, deliveryPromo, deliveryPrice, deliveryPromoPrice, hasOldSofaCollection } = storeToRefs(cartStore);
const { selectedPaymentMethod } = useAdyenDropin();

const gtm = useGtm();
const additionalInfoMaxLength = 25;
const disablePremiumServicesButton = ref(false);
const addressModal = ref(false);
const fakeOnMounted = ref(false);
const { isDesktopViewport } = useMq('md');
const { isNewFormMounted, shouldLoadPaymentsOnCheckout, formDisplay, shouldAllowPayments, disablePayments } = useAdyenDropin();
const { status, deliveries, oldSofaCollection } = storeToRefs(useCheckoutStore());
const {
  formValues,
  submitForm,
  liveSubmitForm,
  phonePrefixCode,
  vatNumberAndCompanyNameVisible,
  additionalAddressBoxVisible,
  formLoaded,
  B2B,
  orderId
} = await handleFormValues();
await fetchDeliveries();
const { payment_only } = route.query;

if (payment_only === 'true') {
  formDisplay.value = false;
  shouldAllowPayments.value = true;
}

if (status?.value === 'PAYMENT_PENDING') {
  formDisplay.value = false;
}

const handleFloorSelect = () => {
  gtm.push({
    event: 'userInteraction',
    eventType: 'EEC',
    eventCategory: 'checkout',
    eventAction: 'Floor_number',
    eventLabel: 'open'
  });
};

const handleFloorOption = () => {
  gtm.push({
    event: 'userInteraction',
    eventType: 'EEC',
    eventCategory: 'checkout',
    eventAction: 'Floor_number',
    eventLabel: formValues.value.floorNumber
  });
};

const { checkoutSwitchRegionModal, onSuccess } = checkoutRegions(formValues, false, liveSubmitForm);
watch(() => formValues.value.phone, (val) => {
  if (val && /[^\d]/.test(val)) {
    formValues.value.phone = val.replace(/[^\d]/g, '');
  }
});
onUpdated(() => {
  if (fakeOnMounted.value) { return; }
  fakeOnMounted.value = true;
  const { section } = route.query;

  if (section && document.querySelector(`#${section}`)) {
    scrollToElement({ element: `#${section}`, offset: 32 });
  }
});

const failedValidation = (node) => {
  const list = getValidationMessages(node);
  const element = document.querySelector(`[name="${[...list][0][0].name}"]`);
  scrollToElement({ element, offset: 64 });
};

const validatePostalCodeDelivery = useDebounceFn(async (postalCode) => {
  const response = await $fetch<{ valid:boolean }>('/api/v2/postal-code-validation/', {
    method: 'GET',
    retry: 0,
    ignoreResponseError: true,
    params: {
      postal_code: postalCode.trim(),
      cart_id: cartId.value
    }
  });

  return response?.valid;
}, 500);

const toast = useToast();
const i18n = useI18n();
const { $retrieveErrorMessageFromResponse } = useNuxtApp();

const isEmailAlreadyRegistered = useDebounceFn(async (email:string) => {
  try {
    const response = await CHECK_IS_ACCOUNT_EXIST(email);

    return response?.is_user_registered === false;
  } catch (e) {
    const errorMessage = $retrieveErrorMessageFromResponse(e?.data) || e?.message || i18n.t('common.error.connection');
    toast.error(errorMessage);
    return true;
  }
}, 500);

const handleSofaRecycling = async () => {
  if (disablePremiumServicesButton.value) {
    return;
  }

  disablePremiumServicesButton.value = true;
  const { error } = await CHANGE_OLD_SOFA_COLLECTION(global, !oldSofaCollection.value.active);

  if (error.value) {
    toast.error($i18n.t('common.error.connection'));
    $logException(error.value);
    return false;
  }

  await fetchUserStatus();
  await fetchDeliveries();
  disablePremiumServicesButton.value = false;
};

const handleClickToAddress = ({
  city,
  postalCode,
  streetAddress1,
  streetAddress2
}) => {
  additionalAddressBoxVisible.value = !!streetAddress2;
  formValues.value.city = city;
  formValues.value.postalCode = postalCode;
  formValues.value.streetAddress1 = streetAddress1;
  formValues.value.streetAddress2 = streetAddress2;
};

const dispatchAddress = ({
  city,
  postalCode,
  street,
  streetNumber
}) => {
  formValues.value.city = city;
  formValues.value.postalCode = postalCode;
  formValues.value.streetAddress1 = `${street ? `${street}` : ''}` + `${streetNumber ? ` ${streetNumber}` : ''}`;
};

</script>

<style lang="scss">

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.copy-with-link {
  a {
    @apply underline;
  }
}

.radio-b2b,
.elevator-radio {
   .formkit-options {
       @apply flex gap-32 h-24;
   }
  .formulate-input-wrapper {
    @apply flex gap-8 leading-none;
  }

  .formulate-input-group-item {
    .formulate-input-wrapper {
      @apply flex items-center gap-8;
    }

    .formulate-input-element {
      @apply p-8 gap-0;
    }
  }
}
.formkit-form {
    > .formkit-messages{
        display: none;
    }
}

.premium-services-item {
        @apply text-left w-full cursor-pointer rounded-8 border border-neutral-500 px-16 py-24;
        &.active {
            @apply border-neutral-900 bg-transparent relative overflow-hidden;
            &::before {
                    @apply absolute right-0 bottom-0 w-20 h-20 bg-neutral-900 bg-no-repeat rounded-tl-6;
                    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="1447.4 687.6 17.6 13.4"><g id="group" transform="translate(1421 687)"><path id="path" fill="%23FFFFFF" class="cls-1" d="M9,16.2,4.8,12,3.4,13.4,9,19,21,7,19.6,5.6Z" transform="translate(23 -5)"/></g></svg>');
                    background-size: 8px 8px;
                    background-position: 6px 6px;
                    content: '';
                }
        }
    }
</style>
